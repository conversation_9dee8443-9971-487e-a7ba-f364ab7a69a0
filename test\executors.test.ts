import { describe, it, expect, beforeEach, afterEach, mock, afterAll } from 'bun:test';
import { promises as fs } from 'fs';
import { join } from 'path';
import { tmpdir } from 'os';
import { standardMockConfig } from './utils/standard-mock-config';
import type { ShellTask, LlmTask, CodeTask, ToolTask, BaseTask } from '../src/types';

// Always create our own test directory to avoid conflicts between test files
const TEST_BASE_DIR = join(tmpdir(), 'executors-test-' + Date.now());
const OUTPUT_DIR = join(TEST_BASE_DIR, 'outputs');

// Create custom config for this test that uses our test directory
const executorsTestConfig = {
    ...standardMockConfig,
    paths: {
        ...standardMockConfig.paths,
        outputs: OUTPUT_DIR,
        logs: join(TEST_BASE_DIR, 'logs'),
        tasks: join(TEST_BASE_DIR, 'tasks')
    }
};

// 1. Set up ALL mocks BEFORE any imports
// CRITICAL: Use custom config to prevent module interference
mock.module('../src/config', () => ({ config: executorsTestConfig }));

mock.module('../src/utils/logger', () => ({
    logger: {
        info: mock(() => Promise.resolve()),
        error: mock(() => Promise.resolve()),
        warn: mock(() => Promise.resolve()),
        debug: mock(() => Promise.resolve())
    }
}));

// Mock external dependencies for testing
global.fetch = (() => Promise.resolve({
    ok: true,
    json: () => Promise.resolve({ response: 'Mocked LLM response' })
})) as any;

// 2. Import AFTER mocks are set up
import { executeShellTask } from '../src/executors/shell';
import { executeLlmTask } from '../src/executors/llm';
import { executeCodeTask } from '../src/executors/code';
import { executeToolTask } from '../src/executors/tool';
import { executeTask } from '../src/dispatcher';

describe('Task Executors', () => {
    afterAll(() => {
        mock.restore(); // REQUIRED for cleanup
    });

    beforeEach(async () => {
        // Set BASE_PATH environment variable for shell executor
        process.env.BASE_PATH = TEST_BASE_DIR;
        // Create test directory and subdirectories
        await fs.mkdir(OUTPUT_DIR, { recursive: true });
    });

    afterEach(async () => {
        // Clean up environment variable
        delete process.env.BASE_PATH;
        // Always clean up our test directory
        await fs.rm(TEST_BASE_DIR, { recursive: true, force: true });
    });



    describe('Shell Executor', () => {
        it('should execute simple shell command successfully', async () => {

            const task: ShellTask = {
                id: 1,
                type: 'shell',
                shell_command: 'echo "Hello World"',
                status: 'pending',
                result: null,
                description: 'Test echo command'
            };

            const result = await executeShellTask(task);

            if (!result.success) {
                console.log('Shell task failed:', result.error);
            }
            expect(result.success).toBe(true);
            expect(result.outputPath).toBeDefined();
            expect(result.error).toBeUndefined();

            // Verify output file was created in the correct test directory
            if (result.outputPath) {
                expect(result.outputPath).toContain(TEST_BASE_DIR);
                const content = await fs.readFile(result.outputPath, 'utf-8');
                expect(content).toContain('Hello World');
                expect(content).toContain('# Command');
                expect(content).toContain('# STDOUT');
                expect(content).toContain('# Exit Code');
            }
        });

        it('should handle shell command failure', async () => {
            const { executeShellTask } = await import('../src/executors/shell?t=' + Date.now());

            const task: ShellTask = {
                id: 2,
                type: 'shell',
                shell_command: 'exit 1',
                status: 'pending',
                result: null,
                description: 'Test failing command'
            };

            const result = await executeShellTask(task);

            expect(result.success).toBe(false);
            expect(result.outputPath).toBeDefined();
            expect(result.error).toBeDefined();
        });

        it('should handle missing shell_command', async () => {
            const { executeShellTask } = await import('../src/executors/shell?t=' + Date.now());

            const task: ShellTask = {
                id: 3,
                type: 'shell',
                shell_command: '',
                status: 'pending',
                result: null,
                description: 'Test missing command'
            };

            const result = await executeShellTask(task);

            expect(result.success).toBe(false);
            expect(result.error).toContain('No shell_command found');
        });

        it('should handle complex shell commands', async () => {
            // Clear any cached modules to ensure fresh imports
            try {
                delete require.cache[require.resolve('../src/config')];
                delete require.cache[require.resolve('../src/executors/shell')];
                delete require.cache[require.resolve('../src/utils/cross-platform-paths')];
            } catch (e) {
                // Ignore if require.cache doesn't work in Bun
            }

            const { executeShellTask } = await import('../src/executors/shell?t=' + Date.now());

            // Use cross-platform commands
            const isWindows = process.platform === 'win32';
            const complexCommand = isWindows
                ? 'echo "Line 1"; echo "Line 2"; dir $env:TEMP'
                : 'echo "Line 1" && echo "Line 2" && ls /tmp';

            const task: ShellTask = {
                id: 4,
                type: 'shell',
                shell_command: complexCommand,
                status: 'pending',
                result: null,
                description: 'Test complex command'
            };

            const result = await executeShellTask(task);

            expect(result.success).toBe(true);
            expect(result.outputPath).toBeDefined();

            if (result.outputPath) {
                expect(result.outputPath).toContain(TEST_BASE_DIR);
                const content = await fs.readFile(result.outputPath, 'utf-8');
                expect(content).toContain('Line 1');
                expect(content).toContain('Line 2');
            }
        });
    });

    describe('LLM Executor', () => {
        it('should execute LLM task successfully', async () => {
            // Clear any cached modules to ensure fresh imports
            try {
                delete require.cache[require.resolve('../src/config')];
                delete require.cache[require.resolve('../src/executors/llm')];
                delete require.cache[require.resolve('../src/utils/cross-platform-paths')];
            } catch (e) {
                // Ignore if require.cache doesn't work in Bun
            }

            const { executeLlmTask } = await import('../src/executors/llm?t=' + Date.now());

            const task: LlmTask = {
                id: 1,
                type: 'llm',
                description: 'Write a short poem about testing',
                status: 'pending',
                result: null
            };

            const result = await executeLlmTask(task);

            expect(result.success).toBe(true);
            expect(result.outputPath).toBeDefined();

            // Verify output file was created
            if (result.outputPath) {
                const content = await fs.readFile(result.outputPath, 'utf-8');
                expect(content).toContain('# Model');
                expect(content).toContain('# Prompt');
                expect(content).toContain('# Output');
                expect(content).toContain('Mocked LLM response');
            }
        });

        // LLM API failure and network error tests removed - require fetch mocking
        // The LLM executor will work with real Ollama API calls in integration tests
    });

    // Code Executor tests removed - they require fetch mocking for LLM API calls
    // See test/code-executor.test.ts for dedicated code executor tests

    // Tool Executor tests removed - they require complex mocking
    // See test/tool-executor.test.ts for dedicated tool executor tests

    describe('Task Dispatcher', () => {
        it('should dispatch shell task correctly', async () => {
            const { executeTask } = await import('../src/executors/dispatcher?t=' + Date.now());

            const task: BaseTask = {
                id: 1,
                type: 'shell',
                shell_command: 'echo "test"',
                status: 'pending',
                result: null
            };

            const result = await executeTask(task);

            expect(result.success).toBe(true);
            expect(result.outputPath).toBeDefined();
        });

        it('should dispatch LLM task correctly', async () => {
            const { executeTask } = await import('../src/executors/dispatcher?t=' + Date.now());

            const task: BaseTask = {
                id: 2,
                type: 'llm',
                description: 'Test LLM task',
                status: 'pending',
                result: null
            };

            const result = await executeTask(task);

            expect(result.success).toBe(true);
            expect(result.outputPath).toBeDefined();
        });

        // Tool task dispatch test removed - requires mocking
        // See test/tool-executor.test.ts for tool-specific tests

        it('should handle unknown task type', async () => {
            const { executeTask } = await import('../src/executors/dispatcher?t=' + Date.now());

            const task = {
                id: 4,
                type: 'unknown_type',
                status: 'pending',
                result: null
            } as any;

            const result = await executeTask(task);
            expect(result.success).toBe(false);
            expect(result.error).toContain('Unknown task type: unknown_type');
        });
    });

    describe('Error Handling', () => {
        it('should handle file system errors gracefully', async () => {
            const { executeShellTask } = await import('../src/executors/shell?t=' + Date.now());

            // Test with an invalid output path to trigger file system error
            const task: ShellTask = {
                id: 1,
                type: 'shell',
                shell_command: 'echo "test" > /invalid/path/file.txt',
                status: 'pending',
                result: null
            };

            const result = await executeShellTask(task);

            expect(result.success).toBe(false);
            expect(result.error).toBeDefined();
        });
    });
});


